#

import psycopg2
import pandas as pd
from pathlib import Path
from io import StringIO
import importlib.util

# --- CONFIGURATION ---
# Choose configuration method: 'yaml' or 'env'
CONFIG_TYPE = 'yaml'  # Change to 'env' to use environment variables instead

# Import config_loaders from the root directory
config_loaders_path = Path(__file__).parent.parent / 'config_loaders.py'
spec = importlib.util.spec_from_file_location(
    "config_loaders", config_loaders_path)
config_loaders = importlib.util.module_from_spec(spec)
spec.loader.exec_module(config_loaders)

# Load database configuration using the selected method
config_loader = config_loaders.get_config_loader(CONFIG_TYPE)
DB_CONFIG = config_loader.load_db_config()
ROOT_FOLDER = Path('./data/SQL')  # Root folder containing subfolders

# --- Connect to PostgreSQL ---
conn = psycopg2.connect(**DB_CONFIG)
conn.autocommit = False  # Explicitly set autocommit to False for transaction control
cur = conn.cursor()


def sanitize_name(name):
    return name.strip().lower().replace(' ', '_').replace('-', '_')


# --- Process folders ---
for folder_path in ROOT_FOLDER.iterdir():
    if not folder_path.is_dir():
        continue

    schema_name = sanitize_name(folder_path.name)

    # Create schema if not exists
    cur.execute(f"CREATE SCHEMA IF NOT EXISTS {schema_name};")
    print(f"[+] Created/Found schema: {schema_name}")

    # --- Process CSV files ---
    for file_path in folder_path.glob('*.csv'):
        table_name = sanitize_name(file_path.stem)

        try:
            # Read CSV
            df = pd.read_csv(file_path)

            if df.empty:
                print(f"    [!] Skipping empty file: {file_path}")
                continue

            # Generate column definitions
            columns = ', '.join([
                f'"{col.strip()}" TEXT' for col in df.columns
            ])

            # Create table
            cur.execute(f'DROP TABLE IF EXISTS {schema_name}."{table_name}";')
            cur.execute(
                f'CREATE TABLE {schema_name}."{table_name}" ({columns});'
            )
            print(f"    [+] Created table: {schema_name}.{table_name}")

            # Insert data using COPY for performance
            buffer = StringIO()
            df.to_csv(buffer, index=False, header=False)
            buffer.seek(0)

            cur.copy_expert(
                sql=f"COPY {schema_name}.\"{table_name}\" FROM STDIN WITH CSV DELIMITER ','",
                file=buffer
            )
            print(
                f"    [+] Inserted {len(df)} rows into: {schema_name}.{table_name}")

            # Commit after each table to ensure data persistence
            conn.commit()
            print(f"    [+] Committed changes for: {schema_name}.{table_name}")

        except Exception as e:
            print(f"    [!] Error processing {file_path}: {str(e)}")
            conn.rollback()
            continue

print("[+] Database setup completed successfully!")
cur.close()
conn.close()
