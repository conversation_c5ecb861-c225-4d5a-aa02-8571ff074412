# config.yaml
# LLM Configuration - You can now use different providers!
# Supported models:
# Google Gemini: gemini-2.0-flash, gemini-1.5-pro, gemini-1.5-flash, gemini-pro
# OpenAI: gpt-4o, gpt-4o-mini, gpt-4-turbo, gpt-4, gpt-3.5-turbo
# Anthropic: claude-3-5-sonnet-20241022, claude-3-5-haiku-20241022, claude-3-opus-20240229

performer_llm:
  model_name: "gemini-2.0-flash"
  temperature: 0.1
  # max_tokens: 1000  # Optional: set max tokens

judge_llm:
  model_name: "gemini-2.0-flash"
  temperature: 0.0
  # max_tokens: 2000  # Optional: set max tokens

# Example configurations for other providers:
# performer_llm:
#   model_name: "gpt-4o-mini"
#   temperature: 0.1
#   api_key: "your-openai-api-key"  # Optional if set in environment
#
# judge_llm:
#   model_name: "claude-3-5-haiku-20241022"
#   temperature: 0.0
#   api_key: "your-anthropic-api-key"  # Optional if set in environment

database:
  name: "proxym"
  user: "postgres"
  password: "DhiaAdmin"
  host: "localhost"
  port: "5432"
